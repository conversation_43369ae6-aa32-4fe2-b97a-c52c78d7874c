/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    CardStatisticsHorizontal: typeof import('./src/@core/components/cards/CardStatisticsHorizontal.vue')['default']
    CardStatisticsVertical: typeof import('./src/@core/components/cards/CardStatisticsVertical.vue')['default']
    CardStatisticsWithImages: typeof import('./src/@core/components/cards/CardStatisticsWithImages.vue')['default']
    ErrorHeader: typeof import('./src/components/ErrorHeader.vue')['default']
    MoreBtn: typeof import('./src/@core/components/MoreBtn.vue')['default']
    ProductsTotalCards: typeof import('./src/@core/components/cards/ProductsTotalCards.vue')['default']
    ProfileCard: typeof import('./src/@core/components/ProfileCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ThemeSwitcher: typeof import('./src/@core/components/ThemeSwitcher.vue')['default']
    UpgradeToPro: typeof import('./src/components/UpgradeToPro.vue')['default']
    VueApexCharts: typeof import('vue3-apexcharts')['default']
  }
}
