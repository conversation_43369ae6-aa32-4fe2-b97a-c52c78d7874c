export default {
  IconBtn: {
    icon: true,
    color: 'default',
    variant: 'text',
  },
  VAlert: {
    VBtn: {
      color: undefined,
    },
  },
  VAvatar: {
    // ℹ️ Remove after next release
    variant: 'flat',
  },
  VBadge: {
    // set v-badge default color to primary
    color: 'primary',
  },
  VBtn: {
    // set v-btn default color to primary
    color: 'primary',
  },
  VChip: {
    elevation: 0,
  },
  VMenu: {
    offset: '2px',
  },
  VPagination: {
    density: 'comfortable',
    showFirstLastPage: true,
    variant: 'tonal',
  },
  VTabs: {
    // set v-tabs default color to primary
    color: 'primary',
    VSlideGroup: {
      showArrows: true,
    },
  },
  VTooltip: {
    // set v-tooltip default location to top
    location: 'top',
  },
  VCheckboxBtn: {
    color: 'primary',
  },
  VCheckbox: {
    // set v-checkbox default color to primary
    color: 'primary',
    density: 'comfortable',
    hideDetails: 'auto',
  },
  VRadioGroup: {
    color: 'primary',
    density: 'comfortable',
    hideDetails: 'auto',
  },
  VRadio: {
    density: 'comfortable',
    hideDetails: 'auto',
  },
  VSelect: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto',
    density: 'comfortable',
  },
  VRangeSlider: {
    // set v-range-slider default color to primary
    color: 'primary',
    thumbLabel: true,
    hideDetails: 'auto',
    trackSize: 6,
    thumbSize: 22,
    elevation: 4,
  },
  VRating: {
    // set v-rating default color to primary
    activeColor: 'warning',
    color: 'disabled',
  },
  VProgressCircular: {
    // set v-progress-circular default color to primary
    color: 'primary',
  },
  VProgressLinear: {
    color: 'primary',
  },
  VSlider: {
    // set v-slider default color to primary
    color: 'primary',
    trackSize: 6,
    hideDetails: 'auto',
    thumbSize: 22,
    elevation: 4,
  },
  VSnackbar: {
    VBtn: {
      size: 'small',
    },
  },
  VTextField: {
    variant: 'outlined',
    density: 'comfortable',
    color: 'primary',
    hideDetails: 'auto',
  },
  VAutocomplete: {
    variant: 'outlined',
    color: 'primary',
    density: 'comfortable',
    hideDetails: 'auto',
  },
  VCombobox: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto',
    density: 'comfortable',
  },
  VFileInput: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto',
    density: 'comfortable',
  },
  VTextarea: {
    variant: 'outlined',
    color: 'primary',
    hideDetails: 'auto',
    density: 'comfortable',
  },
  VSwitch: {
    // set v-switch default color to primary
    inset: true,
    color: 'primary',
    hideDetails: 'auto',
  },
  VNavigationDrawer: {
    touchless: true,
  },
}
