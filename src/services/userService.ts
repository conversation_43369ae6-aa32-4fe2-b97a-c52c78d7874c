import axios from 'axios'

const API_URL = 'https://radifarm.journeymate.tech/api'

let accessToken: null | string = localStorage.getItem('accessToken')

function setAccessToken(token: string) {
  accessToken = token

  localStorage.setItem('accessToken', token)
}

function clearAccessToken() {
  accessToken = null

  localStorage.removeItem('accessToken')
}

export const setAccessTokenWithRemember = (token: string, remember: boolean = false) => {
  accessToken = token
  if (remember)
    localStorage.setItem('accessToken', token)
  else
    sessionStorage.setItem('accessToken', token)
}

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

api.interceptors.request.use(config => {
  if (accessToken)
    config.headers['Authorization'] = `Bearer ${accessToken}`

  return config
})

api.interceptors.response.use(
  res => res,
  async err => {
    if (err.response?.status === 401 && !err.config._retry) {
      err.config._retry = true
      try {
        const res = await axios.post(`${API_URL}/refresh`, {}, {
          headers: {
            'Content-Type': 'application/json',
          },
        })

        setAccessToken(res.data.accessToken)

        err.config.headers['Authorization'] = `Bearer ${res.data.accessToken}`

        return api(err.config)
      }
      catch (refreshErr) {
        clearAccessToken()
        window.location.href = '/login'
      }
    }

    return Promise.reject(err)
  },
)

interface User {
  email?: string | undefined
  password?: string | undefined
  remember?: boolean | undefined
}

// Login
export const login = async (values: User) => {
  const { email, password, remember } = values

  remember ? localStorage.setItem('email', email!) : localStorage.removeItem('email')

  try {
    const response = await axios.post(`${API_URL}/login`, { email, password })

    const token = response.data.data?.token || response.data.accessToken || response.data.token || response.data.access_token || response.data.jwt

    if (!token) {
      console.error('No token found in response:', response.data)

      return 'No authentication token received from server'
    }

    setAccessTokenWithRemember(token, remember)

    return response.data
  }
  catch (error: any) {
    return error.message
  }
}

export const forgotPassword = async (email: string) => {
  try {
    const response = await api.post('/passwordForgot', { email })

    return response.data
  }
  catch (error: any) {
    return error.message
  }
}

export const getProfile = async () => {
  try {
    const response = await api.get('/profile')

    return response.data
  }
  catch (error: any) {
    return error.message
  }
}

export const updateProfile = async (
  name: string,
  email: string,
  phone: string,
  current_password: string,
  new_password: string,
  new_password_confirmation: string,
) => {
  try {
    const response = await api.put('/profile', {
      name,
      email,
      phone,
      current_password,
      new_password,
      new_password_confirmation,
    })

    return response.data
  }
  catch (error: any) {
    return error.message
  }
}

export const getRoles = async () => {
  try {
    const response = await api.get('/roles')

    return response.data
  }
  catch (error: any) {
    return error.message
  }
}

export const logout = () => {
  clearAccessToken()
  window.location.href = '/login'
}

export const isAuthenticated = () => {
  return !!accessToken
}

export const getStoredToken = () => {
  return accessToken
}

export const isTokenExpired = () => {
  const token = localStorage.getItem('accessToken')
  if (!token)
    return true

  try {
    const payload = JSON.parse(atob(token.split('.')[1]))

    return payload.exp * 1000 < Date.now()
  }
  catch {
    return true
  }
}

const initializeToken = () => {
  const localToken = localStorage.getItem('accessToken')
  const sessionToken = sessionStorage.getItem('accessToken')

  if (localToken)
    accessToken = localToken
  else if (sessionToken)
    accessToken = sessionToken
}

initializeToken()
