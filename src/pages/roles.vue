<script setup lang="ts">
import { ref } from 'vue'
import { getRoles } from '@/services/userService'

const loading = ref(false)
const roles = ref<Roles>()

interface Roles {
  id: number
  display_name: string
  description: string
  users_count: number
}

const getAllRoles = async () => {
  loading.value = true
  try {
    const response = await getRoles()
    roles.value = response.data
  }
  catch (error) {
    console.log(error)
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  getAllRoles()
})
</script>

<template>
  <VRow>
    <VCol cols="12">
      <h5 class="text-h5 mb-1">
        Roles List
      </h5>
      <p class="text-body-1 mb-0">
        A role provided access to predefined menus and features so that depending on assigned role an administrator can have access to what he need
      </p>
    </VCol>
    <VCol cols="12">
      <VRow>
        <VCol
          v-for="role in roles"
          :key="role"
          cols="12"
          lg="4"
          sm="6"
        >
          <VCard
            variant="elevated"
            :loading="loading"
          >
            <VCardText class="d-flex align-center">
              <span>Total {{ role.users_count }} users</span>
              <VSpacer />
              <div class="v-avatar-group">
                <VAvatar
                  size="40"
                  variant="flat"
                >
                  <VImg
                    :aspect-ratio="1"
                    src="/images/avatars/avatar-1.png"
                    alt="alt"
                    cover
                  />
                </VAvatar>

                <VAvatar
                  size="40"
                  variant="flat"
                >
                  <VImg
                    :aspect-ratio="1"
                    src="/src/assets/images/avatars/avatar-2.png"
                    alt="alt"
                    cover
                  />
                </VAvatar>
                <VAvatar
                  size="40"
                  variant="flat"
                >
                  <VImg
                    :aspect-ratio="1"
                    src="/src/assets/images/avatars/avatar-3.png"
                    alt="alt"
                    cover
                  />
                </VAvatar>
                <VAvatar
                  size="40"
                  variant="flat"
                >
                  <VImg
                    :aspect-ratio="1"
                    src="/src/assets/images/avatars/avatar-4.png"
                    alt="alt"
                    cover
                  />
                </VAvatar>
              </div>
            </VCardText>

            <VCardText>
              <h5 class="text-h5 mb-1">
                {{ role.display_name }}
              </h5>
              <VCardsubtitle>
                description
              </VCardsubtitle>
              <div class="d-flex align-center mt-1">
                <a href="#">Edit Role</a>
              </div>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VCol>
  </VRow>
</template>
