<script setup lang="ts">
import { ref } from 'vue'
import ProductsTotalCards from '@/@core/components/cards/ProductsTotalCards.vue'

const loading = ref(false)

// const selection = ref(1)
function reserve() {
  loading.value = true
  setTimeout(() => (loading.value = false), 2000)
}

// const desserts = [
//   {
//     product: 'زبادي مثلج',
//     category: 'منتجات الألبان',
//     active: false,
//     sku: 24,
//     price: 4,
//     qty: '1',
//     status: 'منشور',
//   },
//   {
//     product: 'حبوب الجيلي',
//     category: 'حلويات',
//     active: true,
//     sku: 94,
//     price: 0,
//     qty: '0',
//     status: 'مجدول',
//   },
//   {
//     product: 'كيت كات',
//     category: 'شوكولاتة',
//     active: false,
//     sku: 65,
//     price: 7,
//     qty: '6',
//     status: 'مجدول',
//   },
//   {
//     product: 'إكلير',
//     category: 'معجنات',
//     active: true,
//     sku: 23,
//     price: 6,
//     qty: '7',
//     status: 'منشور',
//   },
//   {
//     product: 'خبز الزنجبيل',
//     category: 'مخبوزات',
//     active: true,
//     sku: 49,
//     price: 3.9,
//     qty: '16',
//     status: 'مجدول',
//   },
//   {
//     product: 'ساندويش آيس كريم',
//     category: 'آيس كريم',
//     active: false,
//     sku: 37,
//     price: 4.3,
//     qty: '1',
//     status: 'غير نشط',
//   },
//   {
//     product: 'مصاصة',
//     category: 'حلويات',
//     active: true,
//     sku: 98,
//     price: 0,
//     qty: '2',
//     status: 'غير نشط',
//   },
//   {
//     product: 'كعكة صغيرة',
//     category: 'مخبوزات',
//     active: false,
//     sku: 67,
//     price: 4.3,
//     qty: '8',
//     status: 'غير نشط',
//   },
//   {
//     product: 'قرص العسل',
//     category: 'حلويات',
//     active: true,
//     sku: 87,
//     price: 6.5,
//     qty: '45',
//     status: 'منشور',
//   },
//   {
//     product: 'دونات',
//     category: 'مخبوزات',
//     active: true,
//     sku: 51,
//     price: 4.9,
//     qty: '22',
//     status: 'منشور',
//   },
// ]
// const FakeAPI = {
//   async fetch({ page, itemsPerPage, sortBy, search }) {
//     return new Promise(resolve => {
//       setTimeout(() => {
//         const start = (page - 1) * itemsPerPage
//         const end = start + itemsPerPage
//         const items = desserts.slice().filter(item => {
//           if (search.product && !item.product.toLowerCase().includes(search.product.toLowerCase()))
//             return false

//           // eslint-disable-next-line sonarjs/prefer-single-boolean-return
//           if (search.category && !(item.category >= Number(search.category)))
//             return false

//           return true
//         })
//         if (sortBy.length) {
//           const sortKey = sortBy[0].key
//           const sortOrder = sortBy[0].order
//           items.sort((a, b) => {
//             const aValue = a[sortKey]
//             const bValue = b[sortKey]

//             return sortOrder === 'desc' ? bValue - aValue : aValue - bValue
//           })
//         }
//         const paginated = items.slice(start, end === -1 ? undefined : end)
//         resolve({ items: paginated, total: items.length })
//       }, 500)
//     })
//   },
// }
// const itemsPerPage = ref(5)
// const headers = ref([
//   {
//     title: 'المنتج',
//     align: 'start',
//     sortable: false,
//     key: 'product',
//   },
//   { title: 'الفئة', key: 'category', align: 'end' },
//   { title: 'المخزون', key: 'availability', align: 'end' },
//   { title: 'رمز المنتج', key: 'sku', align: 'end' },
//   { title: 'السعر', key: 'price', align: 'end' },
//   { title: 'الكمية', key: 'qty', align: 'end' },
//   { title: 'الحالة', key: 'status', align: 'end' },
//   { title: 'الإجراءات', key: 'actions', align: 'end' },
// ])
// const serverItems = ref([])
// const loading = ref(true)
// const totalItems = ref(0)
const product = ref('')

// const category = ref('')
// const search = ref('')
// function loadItems({ page, itemsPerPage, sortBy }) {
//   loading.value = true
//   FakeAPI.fetch({ page, itemsPerPage, sortBy, search: { product: product.value, category: category.value } }).then(({ items, total }) => {
//     serverItems.value = items
//     totalItems.value = total
//     loading.value = false
//   })
// }
// watch(product, () => {
//   search.value = String(Date.now())
// })
</script>

<template>
  <div>
    <ProductsTotalCards />

    <VCard variant="elevated">
      <VCardText class="d-flex flex-wrap gap-4">
        <div class="d-flex align-center">
          <VTextField
            v-model="product"
            class="ma-2"
            density="compact"
            placeholder="بحث بإسم المنتج"
            hide-details
            width="200"
          />
        </div>
        <VSpacer />
        <div class="d-flex gap-x-4">
          <!--
            <div class="my-2">
            <VBtn
            color="secondary"
            variant="outlined"
            prepend-icon="ri-external-link-line"
            >
            تصدير
            </VBtn>
            </div>
          -->
          <VBtn
            color="primary"
            variant="elevated"
            prepend-icon="ri-add-line"
            class="my-2"
          >
            إضافة منتج
          </VBtn>
        </div>
      </VCardText>
    </VCard>
    <VCard
      :disabled="loading"
      :loading="loading"
      class="mx-auto my-12"
      max-width="374"
    >
      <template #loader="{ isActive }">
        <VProgressLinear
          :active="isActive"
          color="deep-purple"
          height="4"
          indeterminate
        />
      </template>

      <VImg
        height="250"
        src="https://cdn.vuetifyjs.com/images/cards/cooking.png"
        cover
      />

      <VCardItem>
        <VCardTitle>مقهى باديليكو</VCardTitle>
      </VCardItem>
      <VCardText>
        <div>أطباق صغيرة، سلطات وساندويشات - أجواء حميمي مع 12 مقعد داخلي بالإضافة إلى جلسات الفناء.</div>
      </VCardText>

      <!-- <VDivider class="mx-4 mb-1" /> -->

      <!--
        <VCardTitle>المواعيد المتاحة هذه الليلة</VCardTitle>

        <div class="px-4 mb-2">
        <VChipGroup
        v-model="selection"
        selected-class="bg-deep-purple-lighten-2"
        >
        <VChip>5:30PM</VChip>

        <VChip>7:30PM</VChip>

        <VChip>8:00PM</VChip>

        <VChip>9:00PM</VChip>
        </VChipGroup>
        </div>
      -->

      <VCardActions>
        <VBtn
          color="deep-purple-lighten-2"
          text="أضف الي العربة"
          block
          border
          @click="reserve"
        />
      </VCardActions>
    </VCard>
  </div>
</template>
