<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/yup'
import * as yup from 'yup'
import logo from '@images/logo.png?url'
import { forgotPassword } from '@/services/userService'

const schema = toTypedSchema(yup.object({
  email: yup.string().email('البريد الإلكتروني غير صالح').required('البريد الإلكتروني مطلوب'),
}))

const { defineField, errors, handleSubmit, isSubmitting, values, resetForm } = useForm({
  validationSchema: schema,
  initialValues: {
    email: localStorage.getItem('email') || '',
  },
})

const [email] = defineField('email', {
  props: state => ({
    error: state.errors[0],
  }),
  validateOnModelUpdate: false,
})

const onSubmit = handleSubmit(vals => {
  return new Promise<void>(resolve => {
    setTimeout(() => {
      console.log('Submitted', JSON.stringify(vals, null, 2))
      resolve()
    }, 2000)
  })
    .then(() => {
      forgotPassword(values.email!)
        .then((res: any) => {
          console.log(res.message)
          resetForm()
        })
        .catch(error => {
          console.error(error)
        })
    })
})
</script>

<template>
  <div class="auth-wrapper d-flex align-center justify-center pa-4">
    <VCard
      class="auth-card pa-4 pt-7"
      width="448"
    >
      <VCardItem class="justify-center">
        <img
          :src="logo"
          alt="Radi Farms Logo"
          width="200"
        >
      </VCardItem>

      <VCardText class="pt-2">
        <h4 class="text-h4 mb-1">
          نسيت كلمة المرور؟ 🔒
        </h4>
        <p class="mb-0">
          أدخل بريدك الإلكتروني وسنرسل لك تعليمات لإعادة تعيين كلمة المرور
        </p>
      </VCardText>

      <VCardText>
        <VForm @submit.prevent="onSubmit">
          <VRow>
            <!-- email -->
            <VCol cols="12">
              <VTextField
                v-model="email"
                label="البريد الإلكتروني"
                type="email"
              />
              <div>{{ errors.email }}</div>
            </VCol>

            <!-- login button -->
            <VBtn
              block
              type="submit"
            >
              {{ isSubmitting ? '...إرسال' : "إرسال رابط إعادة التعيين" }}
            </VBtn>

            <div class="v-col v-col-12">
              <RouterLink
                to="/login"
                class="d-flex align-center justify-center"
              >
                <i
                  class="ri-arrow-left-s-line v-icon notranslate v-theme--light v-icon--size-default flip-in-rtl"
                  aria-hidden="true"
                /><span>العودة لتسجيل الدخول</span>
              </RouterLink>
            </div>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </div>
</template>
