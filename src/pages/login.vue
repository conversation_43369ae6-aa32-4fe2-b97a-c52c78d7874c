<script setup lang="ts">
import { useForm } from 'vee-validate'

import { toTypedSchema } from '@vee-validate/yup'
import * as yup from 'yup'
import { router } from '@/plugins/router'
import { login } from '@/services/userService'
import logo from '@images/logo.png?url'

const isPasswordVisible = ref(false)

const schema = toTypedSchema(yup.object({
  email: yup.string().email('البريد الإلكتروني غير صالح').required('البريد الإلكتروني مطلوب'),
  password: yup.string().required('كلمة المرور مطلوبة'),
  remember: yup.boolean(),
}))

const { defineField, errors, handleSubmit, isSubmitting, values } = useForm({
  validationSchema: schema,
  initialValues: {
    email: localStorage.getItem('email') || '',
    password: '',
    remember: false,
  },
})

const [email] = defineField('email', {
  props: state => ({
    error: state.errors[0],
  }),
  validateOnModelUpdate: false,
})

const [password] = defineField('password', {
  validateOnModelUpdate: false,
})

const [remember] = defineField('remember')

const onSubmit = handleSubmit(vals => {
  return new Promise<void>(resolve => {
    setTimeout(() => {
      console.log('Submitted', JSON.stringify(vals, null, 2))
      resolve()
    }, 3000)
  })
    .then(() => {
      login(values)
        .then(() => {
          router.push('/dashboard')
        })
        .catch(error => {
          console.error(error)
        })
    })
})
</script>

<template>
  <div class="auth-wrapper d-flex align-center justify-center pa-4">
    <VCard
      class="auth-card pa-4 pt-7"
      width="448"
    >
      <VCardItem class="justify-center">
        <img
          :src="logo"
          alt="Radi Farms Logo"
          width="200"
        >
      </VCardItem>

      <VCardText class="pt-2">
        <h4 class="text-h4 mb-1">
          مرحباً بك في مزارع راضي! 👋🏻
        </h4>
        <p class="mb-0">
          يرجى تسجيل الدخول إلى حسابك للمتابعة
        </p>
      </VCardText>

      <VCardText>
        <VForm @submit.prevent="onSubmit">
          <VRow>
            <!-- email -->
            <VCol cols="12">
              <VTextField
                v-model="email"
                label="البريد الإلكتروني"
                type="email"
              />
              <div>{{ errors.email }}</div>
            </VCol>

            <!-- password -->
            <VCol cols="12">
              <VTextField
                v-model="password"
                label="كلمة المرور"
                placeholder="············"
                :type="isPasswordVisible ? 'text' : 'password'"
                autocomplete="password"
                :append-inner-icon="isPasswordVisible ? 'ri-eye-off-line' : 'ri-eye-line'"
                @click:append-inner="isPasswordVisible = !isPasswordVisible"
              />
              <div>{{ errors.password }}</div>

              <!-- remember me checkbox -->
              <div class="d-flex align-center justify-space-between flex-wrap my-6">
                <VCheckbox
                  v-model="remember"
                  label="تذكرني"
                />

                <RouterLink
                  class="text-primary"
                  to="/forgot-password"
                >
                  نسيت كلمة المرور؟
                </RouterLink>
              </div>

              <!-- login button -->
              <VBtn
                block
                type="submit"
              >
                {{ isSubmitting ? 'تسجيل...' : 'تسجيل الدخول' }}
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </div>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth";
</style>
