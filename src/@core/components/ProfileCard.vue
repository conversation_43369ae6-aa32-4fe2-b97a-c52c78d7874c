<script setup lang="ts">
import { onMounted, ref, shallowRef } from 'vue'
import { getProfile, updateProfile } from '@/services/userService'

const loading = ref(false)
const email = ref('')
const image = ref('')
const name = ref('')
const phone = ref('')
const role = ref<Role>({})
const wallet = ref('')
const dialog = shallowRef(false)
const currentPassword = ref('')
const newPassword = ref('')
const newPasswordConfirmation = ref('')
const verify = ref(false)
interface Role {
  display_name?: string
  name?: string
  id?: number
  description?: string
}

const getData = async () => {
  loading.value = true

  try {
    const res = await getProfile()
    const { email: emailData, image: imageData, name: nameData, phone: phoneData, role: roleData, wallet: walletData } = res.data

    email.value = emailData
    image.value = imageData
    name.value = nameData
    phone.value = phoneData
    wallet.value = walletData
    role.value = roleData
  }
  catch (err) {
    console.error('Error fetching profile:', err)
  }
  finally {
    loading.value = false
  }
}

const updateData = async () => {
  try {
    const res = await updateProfile(
      name.value,
      email.value,
      phone.value,
      currentPassword.value,
      newPassword.value,
      newPasswordConfirmation.value,
    )
    console.log(res)
  }
  catch (err) {
    console.error('Error fetching profile:', err)
  }
  finally {
    dialog.value = false
    currentPassword.value = ''
    newPassword.value = ''
    newPasswordConfirmation.value = ''
  }
}

const required = (v: string) => {
  return !!v || 'هذا الحقل مطلوب'
}

const matchPassword = (v: string) => {
  return v === newPassword.value || 'كلمة المرور غير متطابقة'
}

onMounted(() => {
  getData()
})
</script>

<template>
  <VRow>
    <VCol class="v-col v-col-12">
      <VCard
        class="mx-auto my-8"
        elevation="16"
        :loading="loading"
      >
        <VCardText class="text-center pt-12 pb-6">
          <VAvatar
            rounded="sm"
            size="120"
            variant="flat"
          >
            <VImg
              :aspect-ratio="1"

              :src="image"
              cover
            />
          </VAvatar>
          <h5 class="text-h5 mt-4">
            {{ name }}
          </h5>
          <VChip
            class="text-error text-capitalize mt-4"
            size="small"
            variant="tonal"
          >
            {{ role.display_name }}
          </VChip>
        </VCardText>

        <VCardText class="d-flex justify-center flex-wrap gap-6 pb-6">
          <div class="d-flex align-center me-8">
            <VAvatar
              rounded="sm"
              variant="tonal"
              class="me-4"
              style=" block-size: 40px;inline-size: 40px;"
            >
              <VIcon
                icon="ri-check-line"
                size="24"
              />
            </VAvatar>
            <div>
              <h6 class="text-h5">
                1,230
              </h6>
              <span>المهام المكتملة</span>
            </div>
          </div>

          <div class="d-flex align-center me-4">
            <VAvatar
              rounded="sm"
              variant="tonal"
              class="me-4"
              style=" block-size: 44px;inline-size: 44px;"
            >
              <VIcon
                icon="ri-briefcase-4-line"
                size="24"
                color="primary"
              />
            </VAvatar>
            <div>
              <h6 class="text-h5">
                568
              </h6>
              <span>المشاريع المكتملة</span>
            </div>
          </div>
        </VCardText>

        <VCardText class="v-card-text pb-6">
          <h5 class="text-h5">
            التفاصيل
          </h5>
          <VDivider class="my-4" />
          <VList
            lines="one"
            class="card-list"
          >
            <VListItem>
              <VListItemTitle class="text-small">
                <span class="font-weight-medium">اسم المستخدم:</span>
                <span class="text-body-1"> {{ name }}</span>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle class="text-small">
                <span class="font-weight-medium"> البريد الإلكتروني:</span>
                <span class="text-body-1">{{ email }}</span>
              </VListItemTitle>
            </VListItem>
            <!-- TODO -->
            <!--
              <VListItem>
              <VListItemTitle class="text-small">
              <span class="font-weight-medium"> الحالة:</span>
              <span class="text-body-1">
              <VChip
              class="text-primary text-capitalize"
              size="small"
              variant="tonal"
              >
              {{ role.name }}
              </VChip>
              </span>
              </VListItemTitle>
              </VListItem>
            -->
            <VListItem>
              <VListItemTitle class="text-small">
                <span class="font-weight-medium">الدور:</span>
                <span class="text-body-1">{{ role.name }}</span>
              </VListItemTitle>
            </VListItem>
            <VListItem>
              <VListItemTitle class="text-small">
                <span class="font-weight-medium">جهة الاتصال:</span>
                <span class="text-body-1">{{ phone }}</span>
              </VListItemTitle>
            </VListItem>
          </VList>
        </VCardText>

        <VCardText class="d-flex justify-center">
          <VBtn
            class="me-4"
            variant="tonal"
            @click="dialog = true"
          >
            تعديل
          </VBtn>

          <VBtn
            class="text-error me-4 border-sm-2"
            variant="outlined"
          >
            تعليق
          </VBtn>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
  <div class="pa-4 text-center">
    <VDialog
      v-model="dialog"
      max-width="600"
    >
      <VForm
        v-model="verify"
        @submit.prevent="updateData"
      >
        <VCard
          prepend-icon="mdi-account"
          title="الملف الشخصي"
        >
          <VCardText>
            <VRow dense>
              <VCol
                cols="12"
                sm="6"
              >
                <VTextField
                  v-model="name"
                  label="الاسم*"
                  :rules="[required]"
                />
              </VCol>

              <VCol
                cols="12"
                sm="6"
              >
                <VTextField
                  v-model="email"
                  label="البريد الإلكتروني*"
                  :rules="[required]"
                />
              </VCol>

              <VCol
                cols="12"
                sm="6"
              >
                <VTextField
                  v-model="phone"
                  label="جهة الاتصال*"
                  :rules="[required]"
                />
              </VCol>

              <VCol
                cols="12"
                sm="6"
              >
                <VTextField
                  v-model="currentPassword"
                  label="كلمة المرور الحالية*"
                  type="password"
                  :rules="[required]"
                />
              </VCol>

              <VCol
                cols="12"
                sm="6"
              >
                <VTextField
                  v-model="newPassword"
                  label="كلمة المرور الجديدة*"
                  type="password"
                  :rules="[required]"
                />
              </VCol>

              <VCol
                cols="12"
                sm="6"
              >
                <VTextField
                  v-model="newPasswordConfirmation"
                  label="تأكيد كلمة المرور الجديدة*"
                  type="password"
                  :rules="[required, matchPassword]"
                />
              </VCol>
            </VRow>

            <small class="text-caption text-medium-emphasis mt-2">*يشير إلى حقل مطلوب</small>
          </VCardText>

          <VDivider />

          <VCardActions class="mt-2">
            <VSpacer />

            <VBtn
              text="إغلاق"
              variant="plain"
              @click="dialog = false"
            />

            <VBtn
              color="primary"
              text="حفظ"
              variant="tonal"
              type="submit"
              :disabled="!verify"
            />
          </VCardActions>
        </VCard>
      </VForm>
    </VDialog>
  </div>
</template>
