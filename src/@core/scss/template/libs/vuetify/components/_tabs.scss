@use "@configured-variables" as variables;
@use "@core/scss/base/mixins";

body {
  .v-tabs {
    .v-tab.v-btn {
      color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
      padding-block: 0;
      padding-inline: 1.375rem;

      .v-icon {
        block-size: 1.125rem;
        font-size: 1.125rem;
        inline-size: 1.125rem;
      }

      &.v-btn--stacked {
        .v-icon {
          block-size: 1.5rem !important;
          font-size: 1.5rem !important;
          inline-size: 1.5rem !important;
        }
      }
    }

    &:not(.v-tabs-pill) {
      &.v-tabs--vertical {
        border-inline-end: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));

        .v-tab__slider {
          inset-inline-end: 0;
          inset-inline-start: unset;
        }
      }

      &.v-tabs--horizontal {
        border-block-end: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));

        .v-tab__slider {
          inset-block-end: 1px;
          inset-block-start: unset;
        }
      }

      .v-tab {
        &.v-tab--selected {
          &.v-btn.v-btn--variant-text {
            &:hover,
            &:active,
            &:focus {
              .v-btn__overlay {
                --v-hover-opacity: 0;
              }
            }
          }
        }

        &:not(.v-tab--selected) {
          &.v-btn.v-btn--variant-text {
            &:hover,
            &:active,
            &:focus {
              color: rgb(var(--v-theme-primary));

              .v-btn__overlay {
                --v-hover-opacity: 0;
              }

              .v-btn__content {
                .v-tab__slider {
                  opacity: var(--v-activated-opacity);
                }
              }
            }
          }
        }
      }
    }

    &.v-tabs-pill {
      .v-slide-group__content {
        gap: 0.25rem;
      }

      &.v-slide-group,
      .v-slide-group__container {
        box-sizing: content-box;
        padding: 1rem;
        margin: -1rem;
      }

      /* stylelint-disable-next-line no-descending-specificity */
      .v-tab {
        &.v-btn {
          border-radius: 0.375rem !important;
        }

        &:not(.v-tab--selected) {
          /* stylelint-disable-next-line no-descending-specificity */
          &.v-btn.v-btn--variant-text {
            &:hover {
              color: rgb(var(--v-theme-primary));
            }
          }
        }

        &.v-tab--selected {
          @include mixins.elevation(2);
        }
      }
    }
  }
}
