<!DOCTYPE html>
<html
  lang="ar"
  dir="rtl"
>
  <head>
    <meta charset="UTF-8" />
    <link
      rel="icon"
      href="/logo.png"
    />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <title>Radi Farms</title>
    <link
      rel="stylesheet"
      type="text/css"
      href="/loader.css"
    />
  </head>

  <body>
    <div id="app">
      <div id="loading-bg">
        <div class="loading-logo">
          <v-img src="/logo.png" />
        </div>
        <div class="loading">
          <div class="effect-1 effects"></div>
          <div class="effect-2 effects"></div>
          <div class="effect-3 effects"></div>
        </div>
      </div>
    </div>
    <script
      type="module"
      src="/src/main.ts"
    ></script>
    <script>
      const loaderColor = localStorage.getItem('radi-farms-initial-loader-bg') || '#FFFFFF'
      const primaryColor = localStorage.getItem('radi-farms-initial-loader-color') || '#1d1e1c'

      if (loaderColor)
  document.documentElement.style.setProperty('--initial-loader-bg', loaderColor)

      if (primaryColor)
  document.documentElement.style.setProperty('--initial-loader-color', primaryColor)
    </script>
  </body>
</html>
